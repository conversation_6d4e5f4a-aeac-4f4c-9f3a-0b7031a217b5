/**
 * <AUTHOR>
 * @since 2025/08/01 8:41
 */
import {Button, notification} from 'ant-design-vue';
import {h} from 'vue';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
let checkVersionTimeId: any;
let currentVersion = 0;


/**
 * 自动检查版本
 */
export function autoCheckVersion() {

    fetch('version.json')
        .then(response => response.json())
        .then((data) => {
            currentVersion = data.version
            setTimeout(() => {
                check();
                checkVersionTimeId = setInterval(() => {
                    check();
                }, 10000);
            }, 2000);
        })
}

/**
 * 自动检查版本
 */
/**
 * 自动检查版本
 */
export function check() {
    fetch(window.location.origin + window.location.pathname + 'version.json')
        .then(response => response.json())
        .then((data) => {
            let newVersion = data.version;
            if (newVersion !== currentVersion) {
                const close = () => {
                    window.location.reload()
                };

                // 添加唯一标识符用于关闭通知
                const key = `open${Date.now()}`;

                notification.open({
                    message: h('div', {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px'
                        }
                    }, [
                        h(ExclamationCircleOutlined, {
                            style: {
                                color: '#faad14'
                            }
                        }),
                        h('span', '有新的版本可用！')
                    ]),
                    description: h('div', {
                        style: {
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '8px'
                        }
                    }, [
                        h('div', {
                            style: {
                                fontSize: '14px',
                                color: '#494949',
                                marginTop: '4px'
                            }
                        }, '请确认页面内容已保存，升级后当前页面内容将丢失')
                    ]),
                    btn: () =>
                        h(
                            Button,
                            {
                                type: 'primary',
                                size: 'small',
                                onClick: () => close(),
                            },
                            {default: () => '升级更新'},
                        ),
                    key,
                    duration: 0,
                });

                clearInterval(checkVersionTimeId);
            }
        }).catch(function (error) {
        console.log(error);
    });
}


